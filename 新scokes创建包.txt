curl ^"http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/xZW7Jy53^" ^
  -H ^"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7^" ^
  -H ^"Accept-Language: zh-CN,zh;q=0.9^" ^
  -H ^"Cache-Control: max-age=0^" ^
  -H ^"Connection: keep-alive^" ^
  -H ^"Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryZ6beQ7RZRnZeXQhY^" ^
  -b ^"sysauth_http=60c8ac3fa47d8535ed3c4ed396c2ec4c^" ^
  -H ^"Origin: http://*************^" ^
  -H ^"Referer: http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/xZW7Jy53^" ^
  -H ^"Upgrade-Insecure-Requests: 1^" ^
  -H ^"User-Agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36^" ^
  --data-raw ^"------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"token^\^"^

^

45ebc2f84cb5a43ee0f6f05d9e5d943d^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbi.submit^\^"^

^

1^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbi.cbe.passwall2.xZW7Jy53.enabled^\^"^

^

1^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbid.passwall2.xZW7Jy53.enabled^\^"^

^

1^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbid.passwall2.xZW7Jy53.node^\^"^

^

8ip9LMdt^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbi.cbe.passwall2.xZW7Jy53.bind_local^\^"^

^

1^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbid.passwall2.xZW7Jy53.port^\^"^

^

10002^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbid.passwall2.xZW7Jy53.http_port^\^"^

^

0^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbi.cbe.passwall2.xZW7Jy53.log^\^"^

^

1^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbid.passwall2.xZW7Jy53.log^\^"^

^

1^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbi.cbe.passwall2.xZW7Jy53.enable_autoswitch^\^"^

^

1^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY^

Content-Disposition: form-data; name=^\^"cbi.apply^\^"^

^

1^

------WebKitFormBoundaryZ6beQ7RZRnZeXQhY--^

^" ^
  --insecure