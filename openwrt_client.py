#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenWrt Passwall 客户端
处理与OpenWrt系统的通信
"""

import requests
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from config import Config
from html_parser import PasswallHtmlParser
from logger import request_logger

class OpenWrtClient:
    def __init__(self, host, username, password):
        # 确保host包含协议前缀
        if not host.startswith(('http://', 'https://')):
            host = f"http://{host}"
        self.host = host.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        })
        self.token = None
        self.session_id = None
        self.parser = PasswallHtmlParser()
        
    def login(self):
        """登录OpenWrt后台"""
        try:
            request_logger.log_message("开始登录OpenWrt后台", "INFO")

            # 构建登录URL
            login_url = f"{self.host}/cgi-bin/luci/"

            # 准备登录数据
            login_data = {
                'luci_username': self.username,
                'luci_password': self.password
            }

            # 记录请求信息
            request_logger.log_request("POST", login_url,
                                     headers=dict(self.session.headers),
                                     data=login_data)

            # 发送登录请求
            response = self.session.post(login_url, data=login_data, allow_redirects=False, timeout=10)

            # 记录响应信息
            request_logger.log_response(response, "登录请求")

            # 检查是否登录成功（通过Set-Cookie头判断）
            if response.status_code == 302 and 'sysauth_http' in response.headers.get('Set-Cookie', ''):
                # 提取session ID
                cookie_header = response.headers.get('Set-Cookie', '')
                self.session_id = self.parser.extract_session_id(cookie_header)

                if self.session_id:
                    request_logger.log_message(f"✅ 登录成功！Session ID: {self.session_id}", "SUCCESS")
                    print(f"✅ 登录成功！获取到sysauth_http: {self.session_id}")
                    return True
                else:
                    request_logger.log_message("❌ 登录失败：无法提取session ID", "ERROR")
                    return False
            else:
                request_logger.log_message(f"❌ 登录失败 - 状态码: {response.status_code}, Cookie: {response.headers.get('Set-Cookie', 'None')}", "ERROR")
                return False

        except Exception as e:
            request_logger.log_error(e, "登录过程中发生异常")
            return False
            
    def get_nodes(self):
        """获取节点列表"""
        try:
            # 优先访问节点列表页面
            import time
            timestamp = int(time.time() * 1000)  # 毫秒时间戳
            node_list_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/node_list"

            # 记录请求
            request_logger.log_request("GET", node_list_url, headers=dict(self.session.headers))

            # 添加时间戳参数防止缓存
            response = self.session.get(node_list_url, params={'_': timestamp})

            # 记录响应
            request_logger.log_response(response)

            if response.status_code == 200:
                # 使用新的节点列表页面提取方法
                nodes = self.parser.extract_nodes(response.text, is_node_list_page=True)

                if nodes:
                    request_logger.log_message(f"✅ 从节点列表页面成功获取到 {len(nodes)} 个节点", "SUCCESS")
                    return nodes
                else:
                    request_logger.log_message("⚠️ 节点列表页面未找到节点，尝试备用方法", "WARNING")
            else:
                request_logger.log_message(f"⚠️ 访问节点列表页面失败，状态码: {response.status_code}，尝试备用方法", "WARNING")

            # 备用方法：访问主Passwall2页面
            passwall_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2"

            # 记录请求
            request_logger.log_request("GET", passwall_url, headers=dict(self.session.headers))

            response = self.session.get(passwall_url)

            # 记录响应
            request_logger.log_response(response)

            if response.status_code != 200:
                request_logger.log_message(f"❌ 访问Passwall2页面失败，状态码: {response.status_code}", "ERROR")
                return []

            # 提取token
            token = self.parser.extract_token(response.text)
            if token:
                self.token = token
                request_logger.log_message(f"🔑 提取到的token: {token}", "INFO")
            else:
                request_logger.log_message("❌ 未能提取到token", "ERROR")

            # 提取已存在的socks配置
            existing_socks = self.parser.extract_existing_socks_configs(response.text)
            if existing_socks:
                request_logger.log_message(f"📋 已存在的socks配置: {existing_socks}", "INFO")
            else:
                request_logger.log_message("📋 未找到已存在的socks配置", "INFO")

            # 解析节点信息（使用备用方法）
            nodes = self.parser.extract_nodes(response.text, is_node_list_page=False)

            # 如果没有找到节点，返回一些测试节点用于演示
            if not nodes:
                request_logger.log_message("⚠️ 未找到节点，使用测试节点", "WARNING")
                test_nodes = [
                    {'id': 'NHbySajH', 'name': '香港节点1', 'region': '香港', 'type': 'Xray', 'address': '***********', 'port': '443'},
                    {'id': 'test_us_1', 'name': '美国节点1', 'region': '美国', 'type': 'Xray', 'address': '***********', 'port': '443'},
                    {'id': 'test_jp_1', 'name': '日本节点1', 'region': '日本', 'type': 'Xray', 'address': '***********', 'port': '443'},
                ]
                return test_nodes

            request_logger.log_message(f"✅ 成功获取到 {len(nodes)} 个节点", "SUCCESS")
            return nodes

        except Exception as e:
            error_msg = f"获取节点列表失败: {e}"
            print(error_msg)
            request_logger.log_error(e, "获取节点列表过程中发生异常")
            return []

    def get_token_and_socks_config(self):
        """获取token和已存在的socks配置"""
        try:
            # 访问Passwall2设置页面
            import time
            timestamp = int(time.time() * 1000)  # 毫秒时间戳
            passwall_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/settings"

            # 记录请求
            request_logger.log_request("GET", passwall_url, headers=dict(self.session.headers))

            # 添加时间戳参数防止缓存
            response = self.session.get(passwall_url, params={'_': timestamp})

            # 记录响应
            request_logger.log_response(response)

            # 保存获取token页面的完整响应到本地文件
            self._save_token_page_response(response)

            if response.status_code != 200:
                request_logger.log_message(f"❌ 访问Passwall2主页面失败，状态码: {response.status_code}", "ERROR")
                return None, []

            # 提取token
            token = self.parser.extract_token(response.text)
            if token:
                self.token = token
                request_logger.log_message(f"🔑 成功提取token: {token}", "SUCCESS")
                print(f"🔑 获取到token: {token}")
            else:
                request_logger.log_message("❌ 未能提取到token", "ERROR")
                print("❌ 未能提取到token")

            # 提取已存在的socks配置
            existing_socks = self.parser.extract_existing_socks_configs(response.text)
            if existing_socks:
                request_logger.log_message(f"📋 已存在的socks配置: {existing_socks}", "SUCCESS")
                print(f"📋 找到 {len(existing_socks)} 个已存在的socks配置:")
                for i, config_id in enumerate(existing_socks, 1):
                    print(f"  {i}. {config_id}")
            else:
                request_logger.log_message("📋 未找到已存在的socks配置", "INFO")
                print("📋 未找到已存在的socks配置")

            return token, existing_socks

        except Exception as e:
            error_msg = f"获取token和socks配置失败: {e}"
            print(error_msg)
            request_logger.log_error(e, "获取token和socks配置过程中发生异常")
            return None, []

    def assign_ports_to_nodes(self, nodes):
        """为所有节点分配端口号并保存到nodes_data.json"""
        from config import REGION_PORT_MAP
        import json

        # 按地区统计节点数量，用于端口分配
        region_counters = {}

        for node in nodes:
            region = node['region']

            # 初始化地区计数器
            if region not in region_counters:
                region_counters[region] = 0

            # 获取地区基础端口
            base_port = REGION_PORT_MAP.get(region, 30000)  # 默认30000+

            # 分配端口：基础端口 + 当前地区的节点序号
            region_counters[region] += 1
            assigned_port = base_port + region_counters[region]

            # 将端口添加到节点数据中
            node['socks_port'] = assigned_port

            print(f"🔌 {node['name']} ({region}) -> 端口 {assigned_port}")

        # 保存更新后的节点数据到JSON文件
        try:
            with open('nodes_data.json', 'w', encoding='utf-8') as f:
                json.dump(nodes, f, ensure_ascii=False, indent=2)
            print(f"💾 已更新nodes_data.json，包含端口分配信息")
            request_logger.log_message(f"💾 已更新nodes_data.json，包含端口分配信息", "INFO")
        except Exception as e:
            print(f"❌ 保存端口分配信息失败: {e}")

        return nodes

    def _save_token_page_response(self, response):
        """保存获取token页面的响应到本地文件"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"token页面响应_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("获取Token页面响应信息\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"时间: {datetime.datetime.now().isoformat()}\n")
                f.write(f"请求URL: {response.url}\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n\n")

                f.write("响应头:\n")
                f.write("-" * 30 + "\n")
                for key, value in response.headers.items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")

                f.write("响应内容 (HTML):\n")
                f.write("-" * 30 + "\n")
                f.write(response.text)
                f.write("\n")

            print(f"💾 Token页面响应已保存到: {filename}")
            request_logger.log_message(f"💾 Token页面响应已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存Token页面响应失败: {e}")

    def _extract_region_from_name(self, name):
        """从节点名称中提取地区信息"""
        region_keywords = {
            '香港': ['香港', 'HK', 'Hong Kong', 'hongkong'],
            '美国': ['美国', 'US', 'USA', 'United States', 'america'],
            '日本': ['日本', 'JP', 'Japan', 'tokyo'],
            '新加坡': ['新加坡', 'SG', 'Singapore'],
            '韩国': ['韩国', 'KR', 'Korea', 'seoul'],
            '台湾': ['台湾', 'TW', 'Taiwan'],
            '英国': ['英国', 'UK', 'Britain', 'london'],
            '德国': ['德国', 'DE', 'Germany', 'berlin'],
            '法国': ['法国', 'FR', 'France', 'paris'],
            '加拿大': ['加拿大', 'CA', 'Canada'],
            '澳大利亚': ['澳大利亚', 'AU', 'Australia'],
            '荷兰': ['荷兰', 'NL', 'Netherlands'],
            '瑞士': ['瑞士', 'CH', 'Switzerland'],
            '俄罗斯': ['俄罗斯', 'RU', 'Russia'],
            '印度': ['印度', 'IN', 'India'],
            '泰国': ['泰国', 'TH', 'Thailand'],
            '马来西亚': ['马来西亚', 'MY', 'Malaysia'],
            '菲律宾': ['菲律宾', 'PH', 'Philippines'],
            '越南': ['越南', 'VN', 'Vietnam'],
            '印尼': ['印尼', 'ID', 'Indonesia'],
        }

        name_upper = name.upper()
        for region, keywords in region_keywords.items():
            for keyword in keywords:
                if keyword.upper() in name_upper:
                    return region

        return '其他'
            
    def get_existing_socks(self):
        """获取现有的Socks配置"""
        try:
            # 访问Passwall2页面
            passwall_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2"
            response = self.session.get(passwall_url)
            
            if response.status_code != 200:
                return []
                
            # 解析HTML获取现有的Socks配置
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找删除按钮或配置项
            # 根据您提供的删除包信息，查找类似 cbi.rts.passwall2.xxx 的项
            existing_configs = []
            
            # 查找所有包含删除操作的表单元素
            delete_inputs = soup.find_all('input', {'name': re.compile(r'cbi\.rts\.passwall2\.')})
            for input_elem in delete_inputs:
                config_id = input_elem.get('name').replace('cbi.rts.passwall2.', '')
                existing_configs.append(config_id)
                
            return existing_configs
            
        except Exception as e:
            print(f"获取现有Socks配置失败: {e}")
            return []
            
    def delete_existing_socks(self, existing_configs):
        """删除现有的Socks配置"""
        try:
            if not existing_configs:
                return True

            # 构建删除请求
            delete_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2"

            # 准备删除数据 - 使用multipart/form-data格式
            delete_files = {
                'token': (None, self.token),
                'cbi.submit': (None, '1')
            }

            # 添加每个配置的删除标记
            for config_id in existing_configs:
                delete_files[f'cbi.rts.passwall2.{config_id}'] = (None, '删除')

            # 保存删除请求数据包到本地文件
            self._save_delete_request_packet(delete_url, delete_files, existing_configs)

            # 记录请求
            request_logger.log_request("POST", delete_url, headers=dict(self.session.headers), files=delete_files)

            # 发送删除请求 - 使用files参数发送multipart/form-data
            response = self.session.post(delete_url, files=delete_files)

            # 记录响应
            request_logger.log_response(response)

            # 保存删除响应数据包到本地文件
            self._save_delete_response_packet(response)

            success = response.status_code == 200 or response.status_code == 302
            if success:
                request_logger.log_message("✅ 删除socks配置请求成功", "SUCCESS")
            else:
                request_logger.log_message(f"❌ 删除socks配置失败，状态码: {response.status_code}", "ERROR")

            return success

        except Exception as e:
            print(f"删除现有配置失败: {e}")
            request_logger.log_error(e, "删除socks配置过程中发生异常")
            return False

    def _save_delete_request_packet(self, url, files_data, existing_configs):
        """保存删除请求数据包到本地文件"""
        try:
            import datetime
            from urllib.parse import urlparse

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"删除请求包_{timestamp}.txt"

            # 解析URL
            parsed_url = urlparse(url)

            # 生成boundary
            boundary = "----WebKitFormBoundaryACE5azrrYYWFcSp0"

            # 构建multipart/form-data请求体
            body_parts = []
            for key, (filename_part, value) in files_data.items():
                part = f"------WebKitFormBoundaryACE5azrrYYWFcSp0\n"
                part += f'Content-Disposition: form-data; name="{key}"\n\n'
                part += f"{value}\n"
                body_parts.append(part)

            # 添加结束boundary
            body_parts.append(f"------WebKitFormBoundaryACE5azrrYYWFcSp0--\n")

            request_body = "".join(body_parts)
            content_length = len(request_body.encode('utf-8'))

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("提交包：\n")

                # 请求行
                f.write(f"POST {parsed_url.path} HTTP/1.1\n")

                # 请求头
                f.write("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n")
                f.write("Accept-Encoding: gzip, deflate\n")
                f.write("Accept-Language: zh-CN,zh;q=0.9\n")
                f.write("Cache-Control: max-age=0\n")
                f.write(f"Content-Length: {content_length}\n")
                f.write(f"Content-Type: multipart/form-data; boundary={boundary}\n")

                # 从session中获取cookie
                cookie_header = ""
                for cookie in self.session.cookies:
                    if cookie.name == 'sysauth_http':
                        cookie_header = f"Cookie: sysauth_http={cookie.value}\n"
                        break
                if cookie_header:
                    f.write(cookie_header)

                f.write(f"Host: {parsed_url.netloc}\n")
                f.write(f"Origin: {parsed_url.scheme}://{parsed_url.netloc}\n")
                f.write("Proxy-Connection: keep-alive\n")
                f.write(f"Referer: {url}\n")
                f.write("Upgrade-Insecure-Requests: 1\n")
                f.write("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n")
                f.write("\n")

                # 请求体
                f.write(request_body)

            print(f"💾 删除请求数据包已保存到: {filename}")
            request_logger.log_message(f"💾 删除请求数据包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存删除请求数据包失败: {e}")

    def _save_delete_response_packet(self, response):
        """保存删除响应数据包到本地文件"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"删除响应包_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("删除响应结果:\n")
                f.write("=" * 30 + "\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n")
                f.write("=" * 30 + "\n")
                f.write("响应内容:\n")
                f.write(response.text)

            print(f"💾 删除响应数据包已保存到: {filename}")
            request_logger.log_message(f"💾 删除响应数据包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存删除响应数据包失败: {e}")

    def create_socks_config(self, node, port):
        """创建新的Socks配置"""
        try:
            # 步骤1: 添加Socks配置
            add_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/settings"

            # 使用multipart/form-data格式
            add_files = {
                'token': (None, self.token),
                'cbi.submit': (None, '1'),
                'cbi.cts.passwall2.socks.': (None, '添加')
            }

            # 保存第一步请求包
            self._save_create_step1_request_packet(add_url, add_files, node)

            # 构建完整的请求头，与日志文件中的格式一致
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Origin': self.host,
                'Referer': add_url,
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 构建multipart/form-data请求体，与日志文件格式完全一致
            import requests_toolbelt
            from requests_toolbelt.multipart.encoder import MultipartEncoder

            multipart_data = MultipartEncoder(
                fields=add_files,
                boundary='----WebKitFormBoundaryhabCBqgi2aWqRIHt'
            )

            headers['Content-Type'] = multipart_data.content_type

            response = self.session.post(add_url, data=multipart_data, headers=headers, allow_redirects=False)

            # 保存第一步响应包
            self._save_create_step1_response_packet(response, node)
            
            if response.status_code != 302:
                return False
                
            # 从重定向URL中提取新配置的ID
            location = response.headers.get('Location', '')
            config_id_match = re.search(r'/socks_config/([^/]+)$', location)
            if not config_id_match:
                return False
                
            config_id = config_id_match.group(1)
            print(f"✅ 成功创建Socks配置条目，ID: {config_id}")
            request_logger.log_message(f"✅ 成功创建Socks配置条目，ID: {config_id}", "SUCCESS")

            # 步骤2: 获取专用token和sessionid
            config_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/socks_config/{config_id}"
            config_token, config_sessionid = self._get_config_token_and_sessionid(config_url, node, config_id)

            if not config_token or not config_sessionid:
                print(f"❌ 获取配置页面token和sessionid失败")
                request_logger.log_message(f"❌ 获取配置页面token和sessionid失败", "ERROR")
                return False

            print(f"🔑 获取到配置专用token: {config_token}")
            print(f"🔑 获取到配置专用sessionid: {config_sessionid}")
            request_logger.log_message(f"🔑 获取到配置专用token: {config_token}", "SUCCESS")
            request_logger.log_message(f"🔑 获取到配置专用sessionid: {config_sessionid}", "SUCCESS")

            # 步骤3: 配置Socks参数

            # 使用multipart/form-data格式，使用专用token和sessionid
            config_files = {
                'token': (None, config_token),  # 使用专用token
                'sessionid': (None, config_sessionid),  # 添加sessionid
                'cbi.submit': (None, '1'),
                'cbi.cbe.passwall2.' + config_id + '.enabled': (None, '1'),
                'cbid.passwall2.' + config_id + '.enabled': (None, '1'),
                'cbid.passwall2.' + config_id + '.node': (None, node['id']),
                'cbi.cbe.passwall2.' + config_id + '.bind_local': (None, '1'),
                'cbid.passwall2.' + config_id + '.port': (None, str(port)),
                'cbid.passwall2.' + config_id + '.http_port': (None, '0'),
                'cbi.cbe.passwall2.' + config_id + '.log': (None, '1'),
                'cbid.passwall2.' + config_id + '.log': (None, '1'),
                'cbi.cbe.passwall2.' + config_id + '.enable_autoswitch': (None, '1'),
                'cbi.apply': (None, '1')  # 恢复 cbi.apply 字段
            }

            # 保存第二步请求包
            self._save_create_step2_request_packet(config_url, config_files, node, config_id, port)

            # 构建完整的请求头，与日志文件中的格式一致
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Origin': self.host,
                'Referer': config_url,
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 构建multipart/form-data请求体，与日志文件格式完全一致
            multipart_data = MultipartEncoder(
                fields=config_files,
                boundary='----WebKitFormBoundaryhabCBqgi2aWqRIHt'
            )

            headers['Content-Type'] = multipart_data.content_type

            response = self.session.post(config_url, data=multipart_data, headers=headers)

            # 保存第二步响应包
            self._save_create_step2_response_packet(response, node, config_id)

            success = response.status_code == 200 or response.status_code == 302
            if success:
                print(f"✅ 成功配置Socks参数: {node['name']} -> 端口 {port}")
                request_logger.log_message(f"✅ 成功配置Socks参数: {node['name']} -> 端口 {port}", "SUCCESS")
            else:
                print(f"❌ 配置Socks参数失败: {node['name']}")
                request_logger.log_message(f"❌ 配置Socks参数失败: {node['name']}", "ERROR")

            return success
            
        except Exception as e:
            print(f"创建Socks配置失败: {e}")
            request_logger.log_error(e, "创建Socks配置过程中发生异常")
            return False

    def _save_create_step1_request_packet(self, url, files_data, node):
        """保存创建步骤1的请求包"""
        try:
            import datetime
            from urllib.parse import urlparse

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"创建请求包1_{safe_name}_{timestamp}.txt"

            parsed_url = urlparse(url)
            boundary = "----WebKitFormBoundarybXIRhYd7tS6iobjo"

            # 构建multipart/form-data请求体
            body_parts = []
            for key, (filename_part, value) in files_data.items():
                part = f"------WebKitFormBoundarybXIRhYd7tS6iobjo\n"
                part += f'Content-Disposition: form-data; name="{key}"\n\n'
                part += f"{value}\n"
                body_parts.append(part)

            body_parts.append(f"------WebKitFormBoundarybXIRhYd7tS6iobjo--\n")
            request_body = "".join(body_parts)
            content_length = len(request_body.encode('utf-8'))

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("提交包：\n")
                f.write(f"POST {parsed_url.path} HTTP/1.1\n")
                f.write("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n")
                f.write("Accept-Encoding: gzip, deflate\n")
                f.write("Accept-Language: zh-CN,zh;q=0.9\n")
                f.write("Cache-Control: max-age=0\n")
                f.write(f"Content-Length: {content_length}\n")
                f.write(f"Content-Type: multipart/form-data; boundary={boundary}\n")

                # Cookie
                for cookie in self.session.cookies:
                    if cookie.name == 'sysauth_http':
                        f.write(f"Cookie: sysauth_http={cookie.value}\n")
                        break

                f.write(f"Host: {parsed_url.netloc}\n")
                f.write(f"Origin: {parsed_url.scheme}://{parsed_url.netloc}\n")
                f.write("Proxy-Connection: keep-alive\n")
                f.write(f"Referer: {url}\n")
                f.write("Upgrade-Insecure-Requests: 1\n")
                f.write("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n")
                f.write("\n")
                f.write(request_body)

            print(f"💾 创建步骤1请求包已保存到: {filename}")
            request_logger.log_message(f"💾 创建步骤1请求包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存创建步骤1请求包失败: {e}")

    def _save_create_step1_response_packet(self, response, node):
        """保存创建步骤1的响应包"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"创建响应包1_{safe_name}_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("创建步骤1响应结果:\n")
                f.write("=" * 30 + "\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n")
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    f.write(f"重定向地址: {location}\n")
                f.write("=" * 30 + "\n")
                f.write("响应内容:\n")
                f.write(response.text)

            print(f"💾 创建步骤1响应包已保存到: {filename}")

        except Exception as e:
            print(f"❌ 保存创建步骤1响应包失败: {e}")

    def _save_create_step2_request_packet(self, url, files_data, node, config_id, port):
        """保存创建步骤2的请求包"""
        try:
            import datetime
            from urllib.parse import urlparse

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"创建请求包2_{safe_name}_{timestamp}.txt"

            parsed_url = urlparse(url)
            boundary = "----WebKitFormBoundaryhabCBqgi2aWqRIHt"

            # 构建multipart/form-data请求体
            body_parts = []
            for key, (filename_part, value) in files_data.items():
                part = f"------WebKitFormBoundaryhabCBqgi2aWqRIHt\n"
                part += f'Content-Disposition: form-data; name="{key}"\n\n'
                part += f"{value}\n"
                body_parts.append(part)

            body_parts.append(f"------WebKitFormBoundaryhabCBqgi2aWqRIHt--\n")
            request_body = "".join(body_parts)
            content_length = len(request_body.encode('utf-8'))

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("提交包：\n")
                f.write(f"POST {parsed_url.path} HTTP/1.1\n")
                f.write("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n")
                f.write("Accept-Encoding: gzip, deflate\n")
                f.write("Accept-Language: zh-CN,zh;q=0.9\n")
                f.write("Cache-Control: max-age=0\n")
                f.write(f"Content-Length: {content_length}\n")
                f.write(f"Content-Type: multipart/form-data; boundary={boundary}\n")

                # Cookie
                for cookie in self.session.cookies:
                    if cookie.name == 'sysauth_http':
                        f.write(f"Cookie: sysauth_http={cookie.value}\n")
                        break

                f.write(f"Host: {parsed_url.netloc}\n")
                f.write(f"Origin: {parsed_url.scheme}://{parsed_url.netloc}\n")
                f.write("Proxy-Connection: keep-alive\n")
                f.write(f"Referer: {url}\n")
                f.write("Upgrade-Insecure-Requests: 1\n")
                f.write("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n")
                f.write("\n")
                f.write(request_body)

            print(f"💾 创建步骤2请求包已保存到: {filename}")
            request_logger.log_message(f"💾 创建步骤2请求包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存创建步骤2请求包失败: {e}")

    def _save_create_step2_response_packet(self, response, node, config_id):
        """保存创建步骤2的响应包"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"创建响应包2_{safe_name}_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("创建步骤2响应结果:\n")
                f.write("=" * 30 + "\n")
                f.write(f"配置ID: {config_id}\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n")
                f.write("=" * 30 + "\n")
                f.write("响应内容:\n")
                f.write(response.text)

            print(f"💾 创建步骤2响应包已保存到: {filename}")

        except Exception as e:
            print(f"❌ 保存创建步骤2响应包失败: {e}")

    def _get_config_token_and_sessionid(self, config_url, node, config_id):
        """获取配置页面的专用token和sessionid"""
        try:
            import time
            import re

            # 添加时间戳防止缓存
            timestamp = int(time.time() * 1000)

            print(f"🔍 获取配置页面token和sessionid: {config_url}")
            request_logger.log_message(f"🔍 获取配置页面token和sessionid: {config_url}", "INFO")

            # 记录GET请求
            request_logger.log_request("GET", config_url, headers=dict(self.session.headers))

            # GET访问配置页面，携带cookies和防缓存参数
            response = self.session.get(config_url, params={'_': timestamp})

            # 记录响应
            request_logger.log_response(response)

            # 保存配置页面响应
            self._save_config_page_response(response, node, config_id)

            if response.status_code == 200:
                # 从HTML中解析token和sessionid
                # 查找类似："],"sessionid":"8227db27c31beba321bb6907611a76f2","token":"74d08e800aa3a0105e2bec908521c8e3","
                pattern = r'"sessionid":"([^"]+)","token":"([^"]+)"'
                match = re.search(pattern, response.text)

                if match:
                    sessionid = match.group(1)
                    token = match.group(2)

                    print(f"✅ 成功解析token: {token}")
                    print(f"✅ 成功解析sessionid: {sessionid}")
                    request_logger.log_message(f"✅ 成功解析token: {token}", "SUCCESS")
                    request_logger.log_message(f"✅ 成功解析sessionid: {sessionid}", "SUCCESS")

                    return token, sessionid
                else:
                    print(f"❌ 未找到token和sessionid模式")
                    request_logger.log_message(f"❌ 未找到token和sessionid模式", "ERROR")
                    return None, None
            else:
                print(f"❌ 访问配置页面失败，状态码: {response.status_code}")
                request_logger.log_message(f"❌ 访问配置页面失败，状态码: {response.status_code}", "ERROR")
                return None, None

        except Exception as e:
            print(f"❌ 获取配置页面token和sessionid异常: {e}")
            request_logger.log_error(e, "获取配置页面token和sessionid过程中发生异常")
            return None, None

    def _save_config_page_response(self, response, node, config_id):
        """保存配置页面响应到本地文件"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"配置页面响应_{safe_name}_{config_id}_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("配置页面响应结果:\n")
                f.write("=" * 50 + "\n")
                f.write(f"节点名称: {node['name']}\n")
                f.write(f"配置ID: {config_id}\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n")
                f.write("=" * 50 + "\n")
                f.write("响应内容:\n")
                f.write(response.text)

            print(f"💾 配置页面响应已保存到: {filename}")
            request_logger.log_message(f"💾 配置页面响应已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存配置页面响应失败: {e}")

    def _save_actual_request_packet(self, request, node, prefix):
        """保存实际发送的请求包"""
        try:
            import datetime
            from urllib.parse import urlparse

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"{prefix}_{safe_name}_{timestamp}.txt"

            parsed_url = urlparse(request.url)

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("实际发送的请求包：\n")
                f.write("=" * 50 + "\n")
                f.write(f"{request.method} {parsed_url.path}")
                if parsed_url.query:
                    f.write(f"?{parsed_url.query}")
                f.write(" HTTP/1.1\n")

                # 写入实际的请求头
                for header_name, header_value in request.headers.items():
                    f.write(f"{header_name}: {header_value}\n")

                f.write("\n")

                # 写入实际的请求体
                if request.body:
                    if isinstance(request.body, bytes):
                        try:
                            body_str = request.body.decode('utf-8')
                        except UnicodeDecodeError:
                            body_str = str(request.body)
                    else:
                        body_str = str(request.body)
                    f.write(body_str)
                else:
                    f.write("(无请求体)")

            print(f"💾 实际请求包已保存到: {filename}")
            request_logger.log_message(f"💾 实际请求包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存实际请求包失败: {e}")

    def get_port_for_region(self, region):
        """根据地区获取端口号"""
        region_ports = Config.REGION_PORTS

        # 查找匹配的地区
        for region_key, base_port in region_ports.items():
            if region_key in region:
                # 使用基础端口，可以根据需要添加递增逻辑
                return base_port

        # 默认端口
        return Config.DEFAULT_PORT

    def get_next_available_port(self, region, existing_ports=None):
        """获取地区的下一个可用端口"""
        if existing_ports is None:
            existing_ports = set()

        base_port = self.get_port_for_region(region)

        # 从基础端口开始查找可用端口
        port = base_port
        while port in existing_ports:
            port += 1
            # 避免端口号过大
            if port > base_port + 1000:
                port = base_port
                break

        return port
