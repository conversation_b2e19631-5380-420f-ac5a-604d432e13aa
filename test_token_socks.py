#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试token和socks配置获取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from openwrt_client import OpenWrtClient

def test_token_and_socks():
    """测试token和socks配置获取"""
    print("🧪 测试token和socks配置获取功能...")
    print("=" * 60)
    
    # 使用默认配置
    host = "*************"
    username = "root"
    password = "password"
    
    try:
        # 创建客户端
        client = OpenWrtClient(host, username, password)
        
        # 登录
        print("🔐 开始登录...")
        if not client.login():
            print("❌ 登录失败")
            return
        print("✅ 登录成功")
        
        # 获取token和socks配置
        print("\n🔑 获取token和socks配置...")
        token, existing_socks = client.get_token_and_socks_config()
        
        print(f"\n📊 结果:")
        print(f"  Token: {token}")
        print(f"  已存在的socks配置数量: {len(existing_socks)}")
        
        if existing_socks:
            print(f"  已存在的socks配置:")
            for i, config_id in enumerate(existing_socks, 1):
                print(f"    {i}. {config_id}")
        else:
            print(f"  无已存在的socks配置")
        
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_token_and_socks()
