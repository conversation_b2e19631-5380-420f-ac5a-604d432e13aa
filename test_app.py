#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本
用于测试应用程序的各个功能模块
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from html_parser import PasswallHtmlParser
from openwrt_client import OpenWrtClient

class TestConfig(unittest.TestCase):
    """测试配置模块"""
    
    def test_default_values(self):
        """测试默认值"""
        self.assertEqual(Config.DEFAULT_HOST, "*************")
        self.assertEqual(Config.DEFAULT_USERNAME, "root")
        self.assertEqual(Config.DEFAULT_PASSWORD, "password")
        
    def test_region_ports(self):
        """测试地区端口映射"""
        self.assertIn("香港", Config.REGION_PORTS)
        self.assertIn("美国", Config.REGION_PORTS)
        self.assertEqual(Config.REGION_PORTS["香港"], 10000)
        self.assertEqual(Config.REGION_PORTS["美国"], 20000)

class TestHtmlParser(unittest.TestCase):
    """测试HTML解析器"""
    
    def setUp(self):
        self.parser = PasswallHtmlParser()
        
    def test_extract_token(self):
        """测试token提取"""
        html = '<input type="hidden" name="token" value="test_token_123" />'
        token = self.parser.extract_token(html)
        self.assertEqual(token, "test_token_123")
        
    def test_extract_session_id(self):
        """测试session ID提取"""
        cookie = "sysauth_http=abc123def456; path=/cgi-bin/luci/"
        session_id = self.parser.extract_session_id(cookie)
        self.assertEqual(session_id, "abc123def456")
        
    def test_extract_region_from_name(self):
        """测试地区提取"""
        test_cases = [
            ("香港节点1", "香港"),
            ("US Server 1", "美国"),
            ("Japan Tokyo", "日本"),
            ("Unknown Server", "其他")
        ]
        
        for name, expected_region in test_cases:
            region = self.parser._extract_region_from_name(name)
            self.assertEqual(region, expected_region)

class TestOpenWrtClient(unittest.TestCase):
    """测试OpenWrt客户端"""
    
    def setUp(self):
        self.client = OpenWrtClient("*************", "root", "password")
        
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.client.host, "*************")
        self.assertEqual(self.client.username, "root")
        self.assertEqual(self.client.password, "password")
        self.assertIsNotNone(self.client.session)
        self.assertIsNotNone(self.client.parser)
        
    def test_get_port_for_region(self):
        """测试端口分配"""
        test_cases = [
            ("香港", 10000),
            ("美国", 20000),
            ("日本", 30000),
            ("其他", Config.DEFAULT_PORT)
        ]
        
        for region, expected_port in test_cases:
            port = self.client.get_port_for_region(region)
            self.assertEqual(port, expected_port)
            
    def test_get_next_available_port(self):
        """测试可用端口获取"""
        existing_ports = {10000, 10001}
        port = self.client.get_next_available_port("香港", existing_ports)
        self.assertEqual(port, 10002)
        
    @patch('requests.Session.post')
    def test_login_success(self, mock_post):
        """测试登录成功"""
        # 模拟成功的登录响应
        mock_response = Mock()
        mock_response.status_code = 302
        mock_response.headers = {
            'Set-Cookie': 'sysauth_http=test_session_id; path=/cgi-bin/luci/'
        }
        mock_post.return_value = mock_response
        
        result = self.client.login()
        self.assertTrue(result)
        self.assertEqual(self.client.session_id, "test_session_id")
        
    @patch('requests.Session.post')
    def test_login_failure(self, mock_post):
        """测试登录失败"""
        # 模拟失败的登录响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {}
        mock_post.return_value = mock_response
        
        result = self.client.login()
        self.assertFalse(result)

def run_basic_tests():
    """运行基础测试"""
    print("OpenWrt Passwall Socks 批量配置工具 - 基础测试")
    print("=" * 50)
    
    # 测试配置
    print("1. 测试配置模块...")
    try:
        assert Config.DEFAULT_HOST == "*************"
        assert "香港" in Config.REGION_PORTS
        print("   ✅ 配置模块测试通过")
    except Exception as e:
        print(f"   ❌ 配置模块测试失败: {e}")
        
    # 测试HTML解析器
    print("2. 测试HTML解析器...")
    try:
        parser = PasswallHtmlParser()
        token = parser.extract_token('<input name="token" value="test123" />')
        assert token == "test123"
        
        session_id = parser.extract_session_id("sysauth_http=abc123; path=/")
        assert session_id == "abc123"
        
        region = parser._extract_region_from_name("香港节点1")
        assert region == "香港"
        
        print("   ✅ HTML解析器测试通过")
    except Exception as e:
        print(f"   ❌ HTML解析器测试失败: {e}")
        
    # 测试OpenWrt客户端
    print("3. 测试OpenWrt客户端...")
    try:
        client = OpenWrtClient("*************", "root", "password")
        assert client.host == "*************"
        
        port = client.get_port_for_region("香港")
        assert port == 10000
        
        print("   ✅ OpenWrt客户端测试通过")
    except Exception as e:
        print(f"   ❌ OpenWrt客户端测试失败: {e}")
        
    print("\n基础测试完成！")

def run_unittest():
    """运行单元测试"""
    print("运行详细单元测试...")
    print("=" * 50)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestConfig))
    suite.addTests(loader.loadTestsFromTestCase(TestHtmlParser))
    suite.addTests(loader.loadTestsFromTestCase(TestOpenWrtClient))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="测试OpenWrt Passwall工具")
    parser.add_argument("--unittest", action="store_true", help="运行详细单元测试")
    args = parser.parse_args()
    
    if args.unittest:
        success = run_unittest()
        sys.exit(0 if success else 1)
    else:
        run_basic_tests()
