OpenWrt Passwall 工具运行日志
==================================================

[2025-08-01 23:03:24] [INFO] 开始登录OpenWrt后台

[2025-08-01 23:03:24] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-01 23:03:24] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=9a1def22cffd3221b53e27e9228305a1; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=9a1def22cffd3221b53e27e9228305a1; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: 9a1def22cffd3221b53e27e9228305a1

响应内容:

==================================================

[2025-08-01 23:03:24] [SUCCESS] ✅ 登录成功！Session ID: 9a1def22cffd3221b53e27e9228305a1

[2025-08-01 23:03:24] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-01 23:03:26] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-01 23:03:26] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点

[2025-08-01 23:03:26] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-01 23:03:26] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-01 23:03:26] [INFO] 💾 Token页面响应已保存到: token页面响应_20250801_230326.txt
[2025-08-01 23:03:26] [SUCCESS] 🔑 成功提取token: c90296ce5f8ce6b7bca4106fe39f0a83
[2025-08-01 23:03:26] [INFO] 📋 未找到已存在的socks配置
[2025-08-01 23:03:26] [INFO] 💾 已更新nodes_data.json，包含端口分配信息
[2025-08-01 23:03:26] [INFO] 💾 创建步骤1请求包已保存到: 创建请求包1_Hong Kong 01_20250801_230326.txt
[2025-08-01 23:03:26] [SUCCESS] ✅ 成功创建Socks配置条目，ID: 1v2iYYHI
[2025-08-01 23:03:26] [INFO] 🔍 获取配置页面token和sessionid: http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/1v2iYYHI

[2025-08-01 23:03:26] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/1v2iYYHI
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-01 23:03:26] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72...
==================================================

[2025-08-01 23:03:26] [INFO] 💾 配置页面响应已保存到: 配置页面响应_Hong Kong 01_1v2iYYHI_20250801_230326.txt
[2025-08-01 23:03:26] [SUCCESS] ✅ 成功解析token: c90296ce5f8ce6b7bca4106fe39f0a83
[2025-08-01 23:03:26] [SUCCESS] ✅ 成功解析sessionid: 9a1def22cffd3221b53e27e9228305a1
[2025-08-01 23:03:26] [SUCCESS] 🔑 获取到配置专用token: c90296ce5f8ce6b7bca4106fe39f0a83
[2025-08-01 23:03:26] [SUCCESS] 🔑 获取到配置专用sessionid: 9a1def22cffd3221b53e27e9228305a1
[2025-08-01 23:03:26] [INFO] 💾 创建步骤2请求包已保存到: 创建请求包2_Hong Kong 01_20250801_230326.txt
[2025-08-01 23:03:27] [SUCCESS] ✅ 成功配置Socks参数: Hong Kong 01 -> 端口 10001
[2025-08-01 23:03:27] [INFO] 💾 创建步骤1请求包已保存到: 创建请求包1_Hong Kong 02_20250801_230327.txt
[2025-08-01 23:03:27] [SUCCESS] ✅ 成功创建Socks配置条目，ID: xZW7Jy53
[2025-08-01 23:03:27] [INFO] 🔍 获取配置页面token和sessionid: http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/xZW7Jy53

[2025-08-01 23:03:27] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/xZW7Jy53
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-01 23:03:27] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72...
==================================================

[2025-08-01 23:03:27] [INFO] 💾 配置页面响应已保存到: 配置页面响应_Hong Kong 02_xZW7Jy53_20250801_230327.txt
[2025-08-01 23:03:27] [SUCCESS] ✅ 成功解析token: c90296ce5f8ce6b7bca4106fe39f0a83
[2025-08-01 23:03:27] [SUCCESS] ✅ 成功解析sessionid: 9a1def22cffd3221b53e27e9228305a1
[2025-08-01 23:03:27] [SUCCESS] 🔑 获取到配置专用token: c90296ce5f8ce6b7bca4106fe39f0a83
[2025-08-01 23:03:27] [SUCCESS] 🔑 获取到配置专用sessionid: 9a1def22cffd3221b53e27e9228305a1
[2025-08-01 23:03:27] [INFO] 💾 创建步骤2请求包已保存到: 创建请求包2_Hong Kong 02_20250801_230327.txt
[2025-08-01 23:03:27] [SUCCESS] ✅ 成功配置Socks参数: Hong Kong 02 -> 端口 10002
[2025-08-01 23:03:27] [INFO] 💾 创建步骤1请求包已保存到: 创建请求包1_Hong Kong 03_20250801_230327.txt
[2025-08-01 23:03:27] [SUCCESS] ✅ 成功创建Socks配置条目，ID: ymW38GdJ
[2025-08-01 23:03:27] [INFO] 🔍 获取配置页面token和sessionid: http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/ymW38GdJ

[2025-08-01 23:03:27] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/ymW38GdJ
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
