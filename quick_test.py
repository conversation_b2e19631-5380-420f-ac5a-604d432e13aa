import re

def _clean_node_name(raw_name):
    """清理节点名称，去掉前缀和乱码"""
    try:
        if not raw_name:
            return "未知节点"

        # 去掉常见的前缀
        prefixes_to_remove = [
            "Xray Trojan：",
            "Xray Trojanï¼",
            "V2ray Trojan：",
            "V2ray Trojanï¼",
            "Trojan：",
            "Trojanï¼",
            "Xray：",
            "Xrayï¼",
            "V2ray：",
            "V2rayï¼"
        ]

        cleaned_name = raw_name
        for prefix in prefixes_to_remove:
            if cleaned_name.startswith(prefix):
                cleaned_name = cleaned_name[len(prefix):].strip()
                break

        # 去掉各种乱码字符
        # 1. 去掉损坏的emoji编码 (如 ð­ð°)
        cleaned_name = re.sub(r'ð[^\s\w]*', '', cleaned_name)

        # 2. 去掉其他常见乱码字符
        cleaned_name = re.sub(r'[ï¼ï½ï¾ï¿]+', '', cleaned_name)

        # 3. 去掉开头的特殊符号，但保留正常的emoji、中文、字母、数字、方括号
        cleaned_name = re.sub(r'^[^\w\s\u4e00-\u9fff\U0001F1E6-\U0001F1FF\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F700-\U0001F77F\U0001F780-\U0001F7FF\U0001F800-\U0001F8FF\U0001F900-\U0001F9FF\U0001FA00-\U0001FA6F\U0001FA70-\U0001FAFF\[\]]+', '', cleaned_name)

        # 4. 清理多余的空格
        cleaned_name = re.sub(r'\s+', ' ', cleaned_name).strip()

        # 如果名称为空，返回默认值
        if not cleaned_name.strip():
            return "未知节点"

        return cleaned_name.strip()

    except Exception as e:
        print(f"❌ 清理节点名称失败: {e}")
        return raw_name if raw_name else "未知节点"

# 测试多个用例
test_cases = [
    "Xray Trojanï¼ð­ð° Hong Kong 06",
    "ð­ð° Hong Kong 05",
    "Xray Trojan：[🇺🇸 USA Seattle 05]",
    "V2ray Trojanï¼ð¯ð° Japan 01",
    "ðºð¸ USA Los Angeles 03",
    "Trojan：香港节点01",
    "ï¼Singapore 03"
]

print("🧪 测试节点名称清理功能:")
print("=" * 60)

for i, test_name in enumerate(test_cases, 1):
    result = _clean_node_name(test_name)
    print(f"测试 {i}:")
    print(f"  原始: '{test_name}'")
    print(f"  清理后: '{result}'")
    print("-" * 40)
