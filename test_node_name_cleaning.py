#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试节点名称清理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from html_parser import <PERSON><PERSON><PERSON>ars<PERSON>

def test_node_name_cleaning():
    """测试节点名称清理功能"""
    parser = HTMLParser()
    
    # 测试用例
    test_cases = [
        {
            'input': 'Xray Trojanï¼ð­ð° Hong Kong 06',
            'expected': '🇭🇰 Hong Kong 06'
        },
        {
            'input': 'Xray Trojan：[🇺🇸 USA Seattle 05]',
            'expected': '[🇺🇸 USA Seattle 05]'
        },
        {
            'input': 'V2ray Trojanï¼[🇯🇵 Japan 01]',
            'expected': '[🇯🇵 Japan 01]'
        },
        {
            'input': 'Trojan：香港节点01',
            'expected': '香港节点01'
        },
        {
            'input': 'Xrayï¼Singapore 03',
            'expected': 'Singapore 03'
        },
        {
            'input': '普通节点名称',
            'expected': '普通节点名称'
        },
        {
            'input': '',
            'expected': '未知节点'
        }
    ]
    
    print("🧪 开始测试节点名称清理功能...")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        input_name = case['input']
        expected = case['expected']
        result = parser._clean_node_name(input_name)
        
        status = "✅ 通过" if result == expected else "❌ 失败"
        if result == expected:
            success_count += 1
        
        print(f"测试 {i}: {status}")
        print(f"  输入: '{input_name}'")
        print(f"  期望: '{expected}'")
        print(f"  结果: '{result}'")
        print("-" * 40)
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查代码")

if __name__ == "__main__":
    test_node_name_cleaning()
